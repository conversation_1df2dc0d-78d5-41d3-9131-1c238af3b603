import 'package:flutter/foundation.dart';

import '../../domain/entities/auth_result.dart';
import '../../domain/entities/auth_user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../utils/biometric_helper.dart';
import '../../view_model/custom_exception.dart';
import '../datasources/auth_local_datasource.dart';
import '../datasources/auth_remote_datasource.dart';
import '../models/auth_result_model.dart';

/// Implementation of AuthRepository following clean architecture principles
///
/// This repository coordinates between remote and local data sources,
/// handles error mapping, and implements the business logic for authentication operations.
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;

  const AuthRepositoryImpl(this._remoteDataSource, this._localDataSource);

  @override
  Future<AuthResult> login(String email, String password) async {
    try {
      // Attempt login with remote data source
      final result = await _remoteDataSource.login(email, password);

      if (result.isSuccess &&
          result.accessToken != null &&
          result.refreshToken != null) {
        // Store tokens locally
        await _localDataSource.storeTokens(
            result.accessToken!, result.refreshToken!);

        // Store credentials for biometric authentication
        await _localDataSource.storeCredentials(email, password);

        // Check and store biometric authentication if available
        await BiometricHelper.checkAndStoreBiometric(email, password);

        // Get user profile
        final user =
            await _remoteDataSource.getUserProfile(result.accessToken!);
        await _localDataSource.storeUserProfile(user);

        // Set two-factor as disabled for successful login
        await _localDataSource.setTwoFactorEnabled(false);

        return AuthResultModel.success(
          user: user,
          accessToken: result.accessToken!,
          refreshToken: result.refreshToken!,
        );
      } else if (result.twoFactorRefCode != null) {
        // Two-factor authentication required
        // Store credentials for later use
        await _localDataSource.storeCredentials(email, password);
        await BiometricHelper.checkAndStoreBiometric(email, password);

        return result;
      } else {
        // Login failed
        return result;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Login error: $e');
      }
      return AuthResultModel.failure('Login failed: ${e.toString()}');
    }
  }

  @override
  Future<AuthResult> loginWithBiometric(String email) async {
    try {
      // Get stored credentials
      final credentials = await _localDataSource.getCredentials();
      if (credentials == null || credentials['password'] == null) {
        return AuthResultModel.failure(
            'No stored credentials for biometric login');
      }

      // Use regular login with stored credentials
      return await login(email, credentials['password']!);
    } catch (e) {
      if (kDebugMode) {
        print('Biometric login error: $e');
      }
      return AuthResultModel.failure('Biometric login failed: ${e.toString()}');
    }
  }

  @override
  Future<void> logout() async {
    try {
      // Get access token for server logout
      final accessToken = await _localDataSource.getAccessToken();

      if (accessToken != null) {
        try {
          // Attempt server logout
          await _remoteDataSource.logout(accessToken);
        } catch (e) {
          // Log error but don't prevent local logout
          if (kDebugMode) {
            print('Server logout failed: $e');
          }
        }
      }

      // Clear local data while preserving biometric and theme preferences
      await _localDataSource.clearAllData(
        preserveBiometric: true,
        preserveTheme: true,
      );
    } catch (e) {
      // Ensure local logout always succeeds
      if (kDebugMode) {
        print('Logout error: $e');
      }
      try {
        await _localDataSource.clearAllData();
      } catch (clearError) {
        if (kDebugMode) {
          print('Failed to clear local data: $clearError');
        }
      }
    }
  }

  @override
  Future<void> globalLogout() async {
    try {
      // Get access token for server logout
      final accessToken = await _localDataSource.getAccessToken();

      if (accessToken != null) {
        // Attempt server global logout
        await _remoteDataSource.globalLogout(accessToken);
      }

      // Clear all local data
      await _localDataSource.clearAllData(
        preserveBiometric: true,
        preserveTheme: true,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Global logout error: $e');
      }
      throw CustomException('Global logout failed: ${e.toString()}');
    }
  }

  @override
  Future<AuthResult> verifyTwoFactor(String refCode, String code) async {
    try {
      // Verify two-factor code with remote data source
      final result = await _remoteDataSource.verifyTwoFactor(refCode, code);

      if (result.isSuccess &&
          result.accessToken != null &&
          result.refreshToken != null) {
        // Store tokens locally
        await _localDataSource.storeTokens(
            result.accessToken!, result.refreshToken!);

        // Get user profile
        final user =
            await _remoteDataSource.getUserProfile(result.accessToken!);
        await _localDataSource.storeUserProfile(user);

        // Set two-factor as enabled
        await _localDataSource.setTwoFactorEnabled(true);

        return AuthResultModel.success(
          user: user,
          accessToken: result.accessToken!,
          refreshToken: result.refreshToken!,
        );
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('Two-factor verification error: $e');
      }
      return AuthResultModel.failure(
          'Two-factor verification failed: ${e.toString()}');
    }
  }

  @override
  Future<String> refreshToken() async {
    try {
      // Get current refresh token
      final refreshToken = await _localDataSource.getRefreshToken();
      if (refreshToken == null) {
        throw CustomException('No refresh token available');
      }

      // Refresh tokens with remote data source
      final tokens = await _remoteDataSource.refreshToken(refreshToken);

      // Store new tokens locally
      await _localDataSource.storeTokens(
          tokens['accessToken']!, tokens['refreshToken']!);

      return tokens['accessToken']!;
    } catch (e) {
      if (kDebugMode) {
        print('Token refresh error: $e');
      }
      throw CustomException('Token refresh failed: ${e.toString()}');
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      // Check if we have valid tokens
      final accessToken = await _localDataSource.getAccessToken();
      if (accessToken == null) return false;

      // For now, just check if token exists
      // In a more sophisticated implementation, you might validate the token
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<AuthUser?> getCurrentUser() async {
    try {
      // Try to get user from local storage first
      final localUser = await _localDataSource.getUserProfile();
      if (localUser != null) {
        return localUser;
      }

      // If not available locally, get from remote
      final accessToken = await _localDataSource.getAccessToken();
      if (accessToken != null) {
        final user = await _remoteDataSource.getUserProfile(accessToken);
        await _localDataSource.storeUserProfile(user);
        return user;
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Get current user error: $e');
      }
      return null;
    }
  }
}
