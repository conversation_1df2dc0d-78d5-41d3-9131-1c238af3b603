import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'secure_storage_service.dart';

/// Abstract service for token management operations
/// 
/// This service handles JWT token operations including validation,
/// refresh, and lifecycle management following clean architecture principles.
abstract class TokenService {
  /// Gets the current access token
  /// 
  /// Returns the current access token if valid, or refreshes it if expired/expiring.
  /// Returns null if no valid token can be obtained.
  /// 
  /// Throws [TokenException] if token operations fail
  Future<String?> getAccessToken();

  /// Gets the current refresh token
  /// 
  /// Returns the stored refresh token or null if none exists
  Future<String?> getRefreshToken();

  /// Stores access and refresh tokens securely
  /// 
  /// Parameters:
  /// - [accessToken]: The access token to store
  /// - [refreshToken]: The refresh token to store
  /// 
  /// Throws [TokenException] if storage fails
  Future<void> storeTokens(String accessToken, String refreshToken);

  /// Clears all stored tokens
  /// 
  /// Throws [TokenException] if clearing fails
  Future<void> clearTokens();

  /// Checks if the current access token is valid (not expired)
  /// 
  /// Returns true if the token exists and is not expired, false otherwise
  Future<bool> isTokenValid();

  /// Checks if the current access token is expiring soon (within 30 minutes)
  /// 
  /// Returns true if the token is expiring within 30 minutes, false otherwise
  Future<bool> isTokenExpiring();

  /// Validates a JWT token and returns its expiry time
  /// 
  /// Parameters:
  /// - [token]: The JWT token to validate
  /// 
  /// Returns the expiry DateTime of the token
  /// 
  /// Throws [TokenException] if the token is invalid
  DateTime getTokenExpiryTime(String token);

  /// Refreshes the access token using the refresh token
  /// 
  /// Returns the new access token
  /// 
  /// Throws [TokenException] if refresh fails
  Future<String> refreshAccessToken();
}

/// Implementation of TokenService
class TokenServiceImpl implements TokenService {
  final SecureStorageService _secureStorage;

  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';

  const TokenServiceImpl(this._secureStorage);

  @override
  Future<String?> getAccessToken() async {
    try {
      if (kDebugMode) {
        print("Getting access token");
      }

      String? accessToken = await _secureStorage.read(_accessTokenKey);
      
      if (accessToken == null || _isTokenExpired(accessToken)) {
        if (kDebugMode) {
          print("Access token expired. Getting new token");
        }
        return await refreshAccessToken();
      } else if (_isTokenExpiring(accessToken)) {
        // Refresh in background after 10 seconds
        if (kDebugMode) {
          print("Token expiring soon. Will refresh in background");
        }
        Future.delayed(const Duration(seconds: 10), () async {
          try {
            await refreshAccessToken();
          } catch (e) {
            if (kDebugMode) {
              print("Background token refresh failed: $e");
            }
          }
        });
        return accessToken;
      }

      if (kDebugMode) {
        print("Access token is valid");
      }
      return accessToken;
    } catch (e) {
      throw TokenException('Failed to get access token: $e');
    }
  }

  @override
  Future<String?> getRefreshToken() async {
    try {
      return await _secureStorage.read(_refreshTokenKey);
    } catch (e) {
      throw TokenException('Failed to get refresh token: $e');
    }
  }

  @override
  Future<void> storeTokens(String accessToken, String refreshToken) async {
    try {
      await _secureStorage.write(_accessTokenKey, accessToken);
      await _secureStorage.write(_refreshTokenKey, refreshToken);
    } catch (e) {
      throw TokenException('Failed to store tokens: $e');
    }
  }

  @override
  Future<void> clearTokens() async {
    try {
      await _secureStorage.delete(_accessTokenKey);
      await _secureStorage.delete(_refreshTokenKey);
    } catch (e) {
      throw TokenException('Failed to clear tokens: $e');
    }
  }

  @override
  Future<bool> isTokenValid() async {
    try {
      String? accessToken = await _secureStorage.read(_accessTokenKey);
      if (accessToken == null) return false;
      return !_isTokenExpired(accessToken);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> isTokenExpiring() async {
    try {
      String? accessToken = await _secureStorage.read(_accessTokenKey);
      if (accessToken == null) return false;
      return _isTokenExpiring(accessToken);
    } catch (e) {
      return false;
    }
  }

  @override
  DateTime getTokenExpiryTime(String token) {
    try {
      String payloadBase64 = token.split('.')[1];
      String normalizedBase64 = base64.normalize(payloadBase64);
      
      String decodedToken = utf8.decode(base64.decode(normalizedBase64));
      Map<String, dynamic> tokenData = json.decode(decodedToken);
      int expiryTime = tokenData['exp'];
      
      return DateTime.fromMillisecondsSinceEpoch(expiryTime * 1000);
    } catch (e) {
      throw TokenException('Invalid token format: $e');
    }
  }

  @override
  Future<String> refreshAccessToken() async {
    // This will be implemented when we create the AuthRepository
    // For now, throw an exception to indicate it needs to be implemented
    throw TokenException('Token refresh not implemented yet - will be handled by AuthRepository');
  }

  bool _isTokenExpired(String token) {
    try {
      DateTime expiryDateTime = getTokenExpiryTime(token);
      DateTime now = DateTime.now();
      return now.isAfter(expiryDateTime);
    } catch (e) {
      return true; // Consider invalid tokens as expired
    }
  }

  bool _isTokenExpiring(String token) {
    try {
      DateTime expiryDateTime = getTokenExpiryTime(token);
      DateTime now = DateTime.now();
      return now.add(const Duration(minutes: 30)).isAfter(expiryDateTime);
    } catch (e) {
      return true; // Consider invalid tokens as expiring
    }
  }
}

/// Exception thrown when token operations fail
class TokenException implements Exception {
  final String message;
  
  const TokenException(this.message);
  
  @override
  String toString() => 'TokenException: $message';
}
